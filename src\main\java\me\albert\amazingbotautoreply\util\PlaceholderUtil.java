package me.albert.amazingbotautoreply.util;

import me.albert.amazingbotautoreply.AmazingBotAutoReply;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 占位符工具类
 */
public class PlaceholderUtil {
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 检查LuckPerms是否已启用
     *
     * @return 是否已启用
     */
    public static boolean isLuckPermsEnabled() {
        return Bukkit.getPluginManager().isPluginEnabled("LuckPerms");
    }

    /**
     * 获取服务器TPS
     *
     * @return TPS值
     */
    public static double getServerTPS() {
        try {
            // 尝试使用反射获取服务器TPS
            Object minecraftServer = getMinecraftServer();
            if (minecraftServer != null) {
                Field recentTpsField = minecraftServer.getClass().getField("recentTps");
                double[] recentTps = (double[]) recentTpsField.get(minecraftServer);
                return recentTps[0]; // 返回最近1分钟的TPS
            }
        } catch (Exception ignored) {
            // 忽略异常
        }

        // 如果无法获取TPS，返回默认值
        return 20.0;
    }

    /**
     * 获取Minecraft服务器实例
     *
     * @return 服务器实例
     */
    private static Object getMinecraftServer() {
        try {
            // 获取CraftServer实例
            Object craftServer = Bukkit.getServer();

            // 获取getServer方法
            Method getServerMethod = craftServer.getClass().getMethod("getServer");

            // 调用getServer方法获取MinecraftServer实例
            return getServerMethod.invoke(craftServer);
        } catch (Exception ignored) {
            // 忽略异常
        }

        return null;
    }

    /**
     * 获取玩家游戏时间
     *
     * @param player 玩家
     * @return 游戏时间
     */
    public static String getPlayerPlaytime(Player player) {
        if (player == null) {
            return "未知";
        }

        // 获取玩家统计数据
        long ticksPlayed = player.getStatistic(org.bukkit.Statistic.PLAY_ONE_MINUTE);

        // 转换为小时、分钟和秒
        long seconds = ticksPlayed / 20;
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long remainingSeconds = seconds % 60;

        return String.format("%d小时%d分钟%d秒", hours, minutes, remainingSeconds);
    }

    /**
     * 获取离线玩家游戏时间
     *
     * @param playerName 玩家名称
     * @return 游戏时间
     */
    public static String getOfflinePlayerPlaytime(String playerName) {
        if (playerName == null || playerName.isEmpty()) {
            return "未知";
        }

        // 获取离线玩家
        OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
        if (offlinePlayer == null || !offlinePlayer.hasPlayedBefore()) {
            return "未知";
        }

        // 如果玩家在线，使用在线玩家方法
        if (offlinePlayer.isOnline() && offlinePlayer.getPlayer() != null) {
            return getPlayerPlaytime(offlinePlayer.getPlayer());
        }

        try {
            // 尝试获取离线玩家统计数据
            // 注意：这可能需要特定的插件支持，如Essentials或Stats
            if (isEssentialsEnabled()) {
                // 如果有Essentials插件，尝试获取玩家游戏时间
                return getEssentialsPlaytime(offlinePlayer);
            }

            // 如果没有特定插件支持，返回默认值
            return "需要玩家在线";
        } catch (Exception e) {
            AmazingBotAutoReply.getInstance().getLogger().warning("获取离线玩家游戏时间时出错: " + e.getMessage());
            return "获取失败";
        }
    }

    /**
     * 获取玩家上次登录时间
     *
     * @param player 玩家
     * @return 上次登录时间
     */
    public static String getPlayerLastLogin(Player player) {
        if (player == null) {
            return "未知";
        }

        // 获取玩家上次登录时间
        long lastPlayed = player.getLastPlayed();
        if (lastPlayed <= 0) {
            return "未知";
        }

        // 格式化时间
        return DATE_FORMAT.format(new Date(lastPlayed));
    }

    /**
     * 获取离线玩家上次登录时间
     *
     * @param playerName 玩家名称
     * @return 上次登录时间
     */
    public static String getOfflinePlayerLastLogin(String playerName) {
        if (playerName == null || playerName.isEmpty()) {
            return "未知";
        }

        // 获取离线玩家
        OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
        if (offlinePlayer == null || !offlinePlayer.hasPlayedBefore()) {
            return "未知";
        }

        // 如果玩家在线，使用在线玩家方法
        if (offlinePlayer.isOnline() && offlinePlayer.getPlayer() != null) {
            return getPlayerLastLogin(offlinePlayer.getPlayer());
        }

        // 获取玩家上次登录时间
        long lastPlayed = offlinePlayer.getLastPlayed();
        if (lastPlayed <= 0) {
            return "未知";
        }

        // 格式化时间
        return DATE_FORMAT.format(new Date(lastPlayed));
    }

    /**
     * 检查Essentials插件是否已启用
     *
     * @return 是否已启用
     */
    public static boolean isEssentialsEnabled() {
        return Bukkit.getPluginManager().isPluginEnabled("Essentials");
    }

    /**
     * 获取Essentials插件实例
     *
     * @return 插件实例
     */
    public static Plugin getEssentialsPlugin() {
        if (isEssentialsEnabled()) {
            return Bukkit.getPluginManager().getPlugin("Essentials");
        }
        return null;
    }

    /**
     * 获取Essentials插件中的玩家游戏时间
     *
     * @param offlinePlayer 离线玩家
     * @return 游戏时间
     */
    public static String getEssentialsPlaytime(OfflinePlayer offlinePlayer) {
        if (offlinePlayer == null) {
            return "未知";
        }

        try {
            // 获取Essentials插件
            Plugin essentials = getEssentialsPlugin();
            if (essentials == null) {
                return "未知";
            }

            // 获取Essentials的User类
            Class<?> essentialsUserClass = Class.forName("com.earth2me.essentials.User");

            // 获取Essentials的Essentials类
            Class<?> essentialsClass = Class.forName("com.earth2me.essentials.Essentials");

            // 获取Essentials实例
            Object essentialsInstance = essentials;

            // 获取getUser方法
            Method getUserMethod = essentialsClass.getMethod("getUser", String.class);

            // 调用getUser方法获取User实例
            Object userInstance = getUserMethod.invoke(essentialsInstance, offlinePlayer.getName());
            if (userInstance == null) {
                return "未知";
            }

            // 获取getBase方法
            Method getPlaytimeMethod = essentialsUserClass.getMethod("getPlaytime");

            // 调用getPlaytime方法获取游戏时间（秒）
            long playtimeSeconds = (long) getPlaytimeMethod.invoke(userInstance);

            // 转换为小时、分钟和秒
            long hours = playtimeSeconds / 3600;
            long minutes = (playtimeSeconds % 3600) / 60;
            long seconds = playtimeSeconds % 60;

            return String.format("%d小时%d分钟%d秒", hours, minutes, seconds);

        } catch (Exception e) {
            AmazingBotAutoReply.getInstance().getLogger().warning("获取Essentials玩家游戏时间时出错: " + e.getMessage());
            return "获取失败";
        }
    }
}
