package me.albert.amazingbotautoreply.utils;

import me.albert.amazingbotautoreply.AmazingBotAutoReply;
import me.clip.placeholderapi.PlaceholderAPI;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.LuckPermsProvider;
import net.luckperms.api.cacheddata.CachedMetaData;
import net.luckperms.api.model.user.User;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.plugin.RegisteredServiceProvider;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.UUID;
import java.util.logging.Level;

/**
 * PlaceholderAPI工具类
 */
public class PlaceholderUtil {

    private static boolean placeholderAPIEnabled = false;
    private static boolean luckPermsEnabled = false;

    /**
     * 初始化
     */
    public static void init() {
        // 检查PlaceholderAPI是否已安装
        placeholderAPIEnabled = Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null;
        if (placeholderAPIEnabled) {
            AmazingBotAutoReply.getInstance().getLogger().info("检测到PlaceholderAPI，已启用变量支持！");
        } else {
            AmazingBotAutoReply.getInstance().getLogger().warning("未检测到PlaceholderAPI，变量功能将不可用。");
        }

        // 检查LuckPerms是否已安装
        luckPermsEnabled = Bukkit.getPluginManager().getPlugin("LuckPerms") != null;
        if (luckPermsEnabled) {
            AmazingBotAutoReply.getInstance().getLogger().info("检测到LuckPerms，已启用称号支持！");
        } else {
            AmazingBotAutoReply.getInstance().getLogger().warning("未检测到LuckPerms，称号功能将不可用。");
        }
    }

    /**
     * 检查PlaceholderAPI是否已启用
     *
     * @return 是否已启用
     */
    public static boolean isPlaceholderAPIEnabled() {
        return placeholderAPIEnabled;
    }

    /**
     * 检查LuckPerms是否已启用
     *
     * @return 是否已启用
     */
    public static boolean isLuckPermsEnabled() {
        return luckPermsEnabled;
    }

    /**
     * 处理PlaceholderAPI变量
     *
     * @param player 玩家
     * @param text   包含变量的文本
     * @return 处理后的文本
     */
    public static String setPlaceholders(OfflinePlayer player, String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 处理自定义变量
        text = processCustomPlaceholders(text);

        // 如果PlaceholderAPI未启用，只处理自定义变量
        if (!isPlaceholderAPIEnabled()) {
            return text;
        }

        try {
            return PlaceholderAPI.setPlaceholders(player, text);
        } catch (Exception e) {
            AmazingBotAutoReply.getInstance().getLogger().log(Level.WARNING, "处理PlaceholderAPI变量时出错", e);
            return text;
        }
    }

    /**
     * 处理自定义变量
     *
     * @param text 包含变量的文本
     * @return 处理后的文本
     */
    private static String processCustomPlaceholders(String text) {
        // 替换在线玩家数量
        if (text.contains("%online_players%")) {
            text = text.replace("%online_players%", String.valueOf(Bukkit.getOnlinePlayers().size()));
        }

        // 替换最大玩家数量
        if (text.contains("%max_players%")) {
            text = text.replace("%max_players%", String.valueOf(Bukkit.getMaxPlayers()));
        }

        // 替换服务器TPS
        if (text.contains("%server_tps%")) {
            // 尝试获取TPS
            String formattedTPS = "20.0"; // 默认值
            try {
                // 尝试使用反射获取TPS
                Object minecraftServer = getMinecraftServer();
                if (minecraftServer != null) {
                    // 尝试获取recentTps方法
                    Method recentTpsMethod = minecraftServer.getClass().getMethod("recentTps");
                    double[] tps = (double[]) recentTpsMethod.invoke(minecraftServer);
                    // 格式化TPS，保留一位小数
                    formattedTPS = String.format("%.1f", Math.min(20.0, tps[0]));
                }
            } catch (Exception e) {
                // 如果出错，使用默认值
                AmazingBotAutoReply.getInstance().getLogger().log(Level.WARNING, "获取服务器TPS时出错，使用默认值", e);
            }
            text = text.replace("%server_tps%", formattedTPS);
        }

        return text;
    }

    /**
     * 获取玩家前缀
     *
     * @param player 玩家
     * @return 玩家前缀
     */
    public static String getPlayerPrefix(OfflinePlayer player) {
        if (!isLuckPermsEnabled() || player == null) {
            return "";
        }

        try {
            LuckPerms luckPerms = LuckPermsProvider.get();
            UUID uuid = player.getUniqueId();

            // 获取用户数据
            User user = luckPerms.getUserManager().loadUser(uuid).join();
            if (user == null) {
                return "";
            }

            // 获取元数据
            CachedMetaData metaData = user.getCachedData().getMetaData();
            String prefix = metaData.getPrefix();

            // 清理用户缓存
            luckPerms.getUserManager().cleanupUser(user);

            return prefix != null ? prefix : "";
        } catch (Exception e) {
            AmazingBotAutoReply.getInstance().getLogger().log(Level.WARNING, "获取玩家前缀时出错", e);
            return "";
        }
    }

    /**
     * 获取玩家后缀
     *
     * @param player 玩家
     * @return 玩家后缀
     */
    public static String getPlayerSuffix(OfflinePlayer player) {
        if (!isLuckPermsEnabled() || player == null) {
            return "";
        }

        try {
            LuckPerms luckPerms = LuckPermsProvider.get();
            UUID uuid = player.getUniqueId();

            // 获取用户数据
            User user = luckPerms.getUserManager().loadUser(uuid).join();
            if (user == null) {
                return "";
            }

            // 获取元数据
            CachedMetaData metaData = user.getCachedData().getMetaData();
            String suffix = metaData.getSuffix();

            // 清理用户缓存
            luckPerms.getUserManager().cleanupUser(user);

            return suffix != null ? suffix : "";
        } catch (Exception e) {
            AmazingBotAutoReply.getInstance().getLogger().log(Level.WARNING, "获取玩家后缀时出错", e);
            return "";
        }
    }

    /**
     * 获取玩家前缀（不包含颜色代码）
     *
     * @param player 玩家
     * @return 玩家前缀（不包含颜色代码）
     */
    public static String getPlayerPrefixWithoutColor(OfflinePlayer player) {
        String prefix = getPlayerPrefix(player);
        // 移除Minecraft颜色代码（§ 和 & 格式）
        return prefix.replaceAll("[§&][0-9a-fk-or]", "");
    }

    /**
     * 获取玩家后缀（不包含颜色代码）
     *
     * @param player 玩家
     * @return 玩家后缀（不包含颜色代码）
     */
    public static String getPlayerSuffixWithoutColor(OfflinePlayer player) {
        String suffix = getPlayerSuffix(player);
        // 移除Minecraft颜色代码（§ 和 & 格式）
        return suffix.replaceAll("[§&][0-9a-fk-or]", "");
    }

    /**
     * 将Minecraft颜色代码转换为纯文本（移除颜色代码）
     *
     * @param text 包含颜色代码的文本
     * @return 移除颜色代码后的文本
     */
    public static String stripColorCodes(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        return text.replaceAll("[§&][0-9a-fk-or]", "");
    }

    /**
     * 将&格式的颜色代码转换为§格式
     *
     * @param text 包含&格式颜色代码的文本
     * @return 转换后的文本
     */
    public static String translateColorCodes(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        return text.replace('&', '§');
    }

    /**
     * 获取MinecraftServer实例
     *
     * @return MinecraftServer实例
     */
    private static Object getMinecraftServer() {
        try {
            // 获取CraftServer实例
            Object craftServer = Bukkit.getServer();

            // 获取getServer方法
            Method getServerMethod = craftServer.getClass().getMethod("getServer");

            // 调用getServer方法获取MinecraftServer实例
            return getServerMethod.invoke(craftServer);
        } catch (Exception e) {
            AmazingBotAutoReply.getInstance().getLogger().log(Level.WARNING, "获取MinecraftServer实例时出错", e);
            return null;
        }
    }

    /**
     * 获取玩家经济信息
     *
     * @param player      玩家
     * @param placeholder 变量
     * @return 处理后的值
     */
    public static String getEconomyInfo(OfflinePlayer player, String placeholder) {
        if (!isPlaceholderAPIEnabled() || player == null || placeholder == null || placeholder.isEmpty()) {
            return "0";
        }

        try {
            // 处理变量
            String result = setPlaceholders(player, placeholder);

            // 如果变量未被处理（返回原始变量），则返回0
            if (result.equals(placeholder)) {
                return "0";
            }

            return result;
        } catch (Exception e) {
            AmazingBotAutoReply.getInstance().getLogger().log(Level.WARNING, "获取玩家经济信息时出错", e);
            return "0";
        }
    }
}
